import*as e from"../../../../core/common/common.js";import*as t from"../../../../core/i18n/i18n.js";import*as i from"../../../../core/platform/platform.js";import*as s from"../../../visual_logging/visual_logging.js";import*as n from"../../legacy.js";import*as r from"../../../components/render_coordinator/render_coordinator.js";const l=new CSSStyleSheet;l.replaceSync('.data-grid{position:relative;border:1px solid var(--sys-color-divider)!important;line-height:120%}.data-grid table{table-layout:fixed;border-spacing:0;border-collapse:separate;height:100%;width:100%}.data-grid .data-container{position:absolute;top:0;bottom:0;left:0;right:0;overflow-x:hidden;overflow-y:overlay;transform:translateZ(0);background-color:var(--sys-color-cdt-base-container)}.data-grid thead{position:sticky;top:0;height:21px;z-index:1}.data-grid .aria-live-label{width:1px;height:1px;overflow:hidden}.data-grid.inline .data-container{position:static}.data-grid .corner{width:14px;padding-right:0;padding-left:0;border-left:0 none transparent!important}.data-grid.inline .corner{display:none}.data-grid.data-grid-fits-viewport .corner{display:none}.data-grid .top-filler-td,\n.data-grid .bottom-filler-td{height:auto!important;padding:0!important}.data-grid table.data{position:absolute;left:0;top:0;right:0;bottom:0;border-top:0 none transparent;table-layout:fixed}.data-grid.inline table.data{position:static}.data-grid tbody tr{display:none;height:20px}.data-grid tbody tr.revealed{display:table-row}.striped-data-grid .revealed.data-grid-data-grid-node:nth-child(odd):not(.dirty):not(.selected),\n.striped-data-grid-starts-with-odd .revealed.data-grid-data-grid-node:nth-child(even):not(.dirty):not(.selected){background-color:var(--sys-color-surface1)}.data-grid td,\n.data-grid th{white-space:nowrap;text-overflow:ellipsis;overflow:hidden;line-height:18px;height:18px;border-left:1px solid var(--sys-color-divider);padding:1px 4px}.data-grid td{vertical-align:top;user-select:text}.data-grid tr:has(th){background-color:var(--sys-color-surface1)}.data-grid th{border-bottom:1px solid var(--sys-color-divider);text-align:left;font-weight:normal;vertical-align:middle}.data-grid th:first-child,\n.data-grid td:first-child{border-left-width:0}.data-grid tr.selected td,\n.data-grid tr.selected th{border-left-color:var(--sys-color-divider-on-tonal-container)}.data-grid td > div,\n.data-grid th > div{white-space:nowrap;text-overflow:ellipsis;overflow:hidden;min-width:8px}.data-grid td.editing > div{text-overflow:clip}.data-grid .center{text-align:center}.data-grid .right{text-align:right}.data-grid th.sortable{position:relative}.data-grid th .sort-order-icon-container{min-width:0;position:absolute;top:1px;right:0;bottom:1px;display:flex;align-items:center}.data-grid th .sort-order-icon{margin-right:4px;margin-bottom:-2px;width:14px;height:14px;display:none;background-color:var(--icon-default)}.data-grid th.sort-ascending .sort-order-icon{display:inline-block;mask-image:var(--image-file-triangle-up)}.data-grid th.sort-descending .sort-order-icon{display:inline-block;mask-image:var(--image-file-triangle-down)}.data-grid th.sort-ascending,\n.data-grid th.sort-descending{padding-right:14px}.data-grid th.sortable:hover{background-color:var(--sys-color-state-hover-on-subtle)}.data-grid .top-filler-td{border-bottom:0 none transparent;line-height:0}.data-grid button{line-height:18px;color:inherit}.data-grid td.disclosure::before{user-select:none;mask-image:var(--image-file-triangle-right);float:left;width:14px;height:14px;margin-right:2px;content:"";position:relative;top:3px;background-color:var(--icon-default);transition:transform 200ms}.data-grid tr:not(.parent) td.disclosure::before{background-color:transparent}.data-grid tr.expanded td.disclosure::before{transform:rotate(90deg)}.data-grid tbody tr.revealed.selected{background-color:var(--sys-color-neutral-container);color:inherit;& ::selection{background-color:var(--sys-color-state-focus-select)}}.data-grid tbody tr.revealed:focus-within{background-color:var(--sys-color-tonal-container)}.data-grid tbody tr.revealed:not(.data-grid-filler-row):not(.selected):hover{background-color:var(--sys-color-state-hover-on-subtle)}.data-grid tbody tr.revealed.selected.dirty{color:var(--sys-color-on-tonal-container)}.data-grid.no-selection:focus-visible{border:1px solid var(--sys-color-state-focus-ring)!important}.data-grid:focus tbody tr.selected{background-color:var(--sys-color-tonal-container);color:var(--sys-color-on-tonal-container)}.data-grid tbody tr.selected.dirty{--override-data-grid-dirty-background-color:var(--sys-color-error);background-color:var(--override-data-grid-dirty-background-color)}.data-grid:focus tr.selected.dirty{--override-data-grid-dirty-background-color:var(--sys-color-error-container)}.data-grid:focus tr.selected .devtools-link{color:var(--sys-color-on-tonal-container)}.data-grid:focus tr.parent.selected td.disclosure::before{background-color:var(--sys-color-on-tonal-container)}.data-grid tr.inactive{color:var(--sys-color-state-disabled);font-style:italic}.data-grid tr.dirty{--override-data-grid-dirty-background-color:var(--sys-color-surface-error);background-color:var(--override-data-grid-dirty-background-color);color:var(--sys-color-error);font-style:normal}.data-grid td.show-more{white-space:normal}.data-grid td.show-more::before{display:none}.data-grid-resizer{position:absolute;top:0;bottom:0;width:5px;z-index:500}@media (forced-colors: active){.sort-order-icon-container .sort-order-icon{forced-color-adjust:none;background-color:ButtonText}.data-grid td.disclosure::before{forced-color-adjust:none;color:ButtonText}.data-grid.no-selection:focus-visible *{color:ButtonText}.data-grid th.sortable:hover *,\n  .data-grid tr.parent.selected td.disclosure::before,\n  .data-grid:focus tr.parent.selected td.disclosure::before,\n  .data-grid tbody tr.parent.revealed:hover td.disclosure::before{color:HighlightText}.data-grid th.sortable:hover .sort-order-icon-container .sort-order-icon{background-color:HighlightText}.striped-data-grid .revealed.data-grid-data-grid-node:nth-child(odd):not(.dirty):not(.selected),\n  .striped-data-grid-starts-with-odd .revealed.data-grid-data-grid-node:nth-child(even):not(.dirty):not(.selected),\n  .request-cookies-view tr.revealed.data-grid-data-grid-node.flagged-cookie-attribute-row:not(.selected):nth-child(2n),\n  .cookies-table tr.revealed.data-grid-data-grid-node.flagged-cookie-attribute-row:not(.selected):nth-child(odd){background-color:canvas}.data-grid.no-selection:focus-visible{forced-color-adjust:none;border-color:Highlight}.data-grid th.sortable:hover,\n  .data-grid tbody tr.revealed:hover,\n  .data-grid tbody tr.revealed.selected,\n  .striped-data-grid .revealed:hover.data-grid-data-grid-node:nth-child(odd):not(.dirty):not(.selected),\n  .striped-data-grid-starts-with-odd .revealed:hover.data-grid-data-grid-node:nth-child(even):not(.dirty):not(.selected),\n  .request-cookies-view tr.revealed:hover.data-grid-data-grid-node.flagged-cookie-attribute-row:not(.selected):nth-child(2n),\n  .cookies-table tr.revealed:hover.data-grid-data-grid-node.flagged-cookie-attribute-row:not(.selected):nth-child(odd){forced-color-adjust:none;background-color:Highlight}.data-grid tbody tr.revealed:hover *,\n  .data-grid tbody tr.revealed.selected *,\n  .data-grid tbody tr.revealed:focus *,\n  .data-grid tbody tr.revealed:hover .heap-object-tag{color:HighlightText}.data-grid th{background-color:canvas;border-color:Highlight}}\n/*# sourceURL=dataGrid.css */\n');const d={expanded:"expanded",collapsed:"collapsed",sRowS:"{PH1} Row {PH2}",rowsS:"Rows: {PH1}",sSUseTheUpAndDownArrowKeysTo:"{PH1} {PH2}, use the up and down arrow keys to navigate and interact with the rows of the table; Use browse mode to read cell by cell.",sortByString:"Sort By",resetColumns:"Reset Columns",headerOptions:"Header Options",refresh:"Refresh",addNew:"Add new",editS:'Edit "{PH1}"',delete:"Delete",levelS:"level {PH1}",checked:"checked",emptyRowCreated:"An empty table row has been created. You may double click or use context menu to edit."},o=t.i18n.registerUIStrings("ui/legacy/components/data_grid/DataGrid.ts",d),a=t.i18n.getLocalizedString.bind(void 0,o),h=new WeakMap,c=new WeakMap,u=new WeakMap,g=new WeakMap,p=new WeakMap;class m extends e.ObjectWrapper.ObjectWrapper{element;displayName;editCallback;deleteCallback;refreshCallback;dataTableHeaders;scrollContainerInternal;dataContainerInternal;dataTable;inline;columnsArray;columns;visibleColumnsArray;cellClass;dataTableHeadInternal;headerRow;dataTableColumnGroup;dataTableBody;topFillerRow;bottomFillerRow;editing;selectedNode;expandNodesWhenArrowing;indentWidth;resizers;columnWidthsInitialized;cornerWidth;resizeMethod;headerContextMenuCallback;rowContextMenuCallback;elementToDataGridNode;disclosureColumnId;sortColumnCell;rootNodeInternal;editingNode;columnWeightsSetting;creationNode;currentResizer;dataGridWidget;constructor(e){super();const{displayName:t,columns:i,editCallback:s,deleteCallback:r,refreshCallback:l}=e;this.element=document.createElement("div"),this.element.classList.add("data-grid"),this.element.tabIndex=0,this.element.addEventListener("keydown",this.keyDown.bind(this),!1),this.element.addEventListener("contextmenu",this.contextMenu.bind(this),!0),this.element.addEventListener("focusin",(e=>{this.updateGridAccessibleNameOnFocus(),e.consume(!0)})),this.element.addEventListener("focusout",(e=>{e.consume(!0)})),n.ARIAUtils.markAsApplication(this.element),this.displayName=t,this.editCallback=s,this.deleteCallback=r,this.refreshCallback=l,this.dataTableHeaders={},this.dataContainerInternal=this.element.createChild("div","data-container"),this.dataTable=this.dataContainerInternal.createChild("table","data"),this.scrollContainerInternal=this.dataContainerInternal,s&&this.dataTable.addEventListener("dblclick",this.ondblclick.bind(this),!1),this.dataTable.addEventListener("mousedown",this.mouseDownInDataTable.bind(this)),this.dataTable.addEventListener("click",this.clickInDataTable.bind(this),!0),this.inline=!1,this.columnsArray=[],this.columns={},this.visibleColumnsArray=i,i.forEach((e=>this.innerAddColumn(e))),this.cellClass=null,this.dataTableColumnGroup=this.dataTable.createChild("colgroup"),this.dataTableHeadInternal=this.dataTable.createChild("thead"),this.headerRow=this.dataTableHeadInternal.createChild("tr"),this.dataTableBody=this.dataTable.createChild("tbody"),this.topFillerRow=this.dataTableBody.createChild("tr","data-grid-filler-row revealed"),n.ARIAUtils.setHidden(this.topFillerRow,!0),this.bottomFillerRow=this.dataTableBody.createChild("tr","data-grid-filler-row revealed"),n.ARIAUtils.setHidden(this.bottomFillerRow,!0),this.setVerticalPadding(0,0,!0),this.refreshHeader(),this.editing=!1,this.selectedNode=null,this.expandNodesWhenArrowing=!1,this.setRootNode(new N),this.setHasSelection(!1),this.indentWidth=15,this.resizers=[],this.columnWidthsInitialized=!1,this.cornerWidth=f,this.resizeMethod="nearest",this.headerContextMenuCallback=null,this.rowContextMenuCallback=null,this.elementToDataGridNode=new WeakMap}firstSelectableNode(){let e=this.rootNodeInternal;for(;e&&!e.selectable;)e=e.traverseNextNode(!0)||void 0;return e}lastSelectableNode(){let e=this.rootNodeInternal,t=this.rootNodeInternal;for(;t;)t.selectable&&(e=t),t=t.traverseNextNode(!0)||void 0;return e}setElementContent(e,t){const i=this.columnIdFromNode(e);if(!i)return;const s=this.columns[i],n=e.parentElement;let r;n&&(r=this.elementToDataGridNode.get(n)),"Boolean"===s.dataType?m.setElementBoolean(e,Boolean(t),r):null!==t&&m.setElementText(e,t,Boolean(s.longText),r)}static setElementText(e,t,s,r){s&&t.length>1e3?(e.textContent=i.StringUtilities.trimEndWithMaxLength(t,1e3),n.Tooltip.Tooltip.install(e,t),h.set(e,t)):(e.textContent=t,n.Tooltip.Tooltip.install(e,""),h.delete(e)),r&&m.updateNodeAccessibleText(r)}static setElementBoolean(e,t,i){e.textContent=t?"✓":"",n.Tooltip.Tooltip.install(e,""),i&&m.updateNodeAccessibleText(i)}static updateNodeAccessibleText(e){let t="",i=e.elementInternal?.children[0]||null;if(i){for(;i&&!i.classList.contains("corner");){let s=null;for(const e of i.classList)if(e.includes("-column")){s=e.substring(0,e.indexOf("-column"));break}if(s&&e.dataGrid){const n=e.dataGrid.columns[s];n&&(t+=`${n.title}: ${i.textContent}, `)}i=i.nextElementSibling}t.length>0&&(t=t.substring(0,t.length-2)),e.nodeAccessibleText=t}}setStriped(e){this.element.classList.toggle("striped-data-grid",e)}setFocusable(e){this.element.tabIndex=e?0:-1,!1===e&&n.ARIAUtils.removeRole(this.element)}setHasSelection(e){this.element.classList.toggle("no-selection",!e)}announceSelectedGridNode(){if(this.element===i.DOMUtilities.deepActiveElement(this.element.ownerDocument)&&this.selectedNode&&this.selectedNode.existingElement()){let e;this.selectedNode.hasChildren()&&(e=this.selectedNode.expanded?a(d.expanded):a(d.collapsed));const t=e?`${this.selectedNode.nodeAccessibleText}, ${e}`:this.selectedNode.nodeAccessibleText;n.ARIAUtils.alert(t)}}updateGridAccessibleNameOnFocus(){let e;if(this.selectedNode&&this.selectedNode.existingElement()){let t="";this.selectedNode.hasChildren()&&(t=this.selectedNode.expanded?a(d.expanded):a(d.collapsed));e=`${a(d.sRowS,{PH1:this.displayName,PH2:t})} ${this.selectedNode.nodeAccessibleText}`}else{if(!this.rootNodeInternal)return;const t=this.enumerateChildren(this.rootNodeInternal,[],1),i=a(d.rowsS,{PH1:t.length});e=a(d.sSUseTheUpAndDownArrowKeysTo,{PH1:this.displayName,PH2:i})}n.ARIAUtils.alert(e)}innerAddColumn(e,t){e.defaultWeight=e.weight;const n=e.id;n in this.columns&&this.innerRemoveColumn(n),void 0===t&&(t=this.columnsArray.length),this.columnsArray.splice(t,0,e),this.columns[n]=e,e.disclosure&&(this.disclosureColumnId=n);const r=document.createElement("th");r.setAttribute("jslog",`${s.tableHeader().track({click:e.sortable,resize:!0}).context(i.StringUtilities.toKebabCase(n))}`),r.className=n+"-column",c.set(r,n),this.dataTableHeaders[n]=r;const l=document.createElement("div");if(e.titleDOMFragment?l.appendChild(e.titleDOMFragment):l.textContent=e.title||null,r.appendChild(l),e.sort&&(r.classList.add(e.sort),this.sortColumnCell=r),e.sortable){r.addEventListener("click",this.clickInHeaderCell.bind(this),!1),r.classList.add("sortable");const e=document.createElement("span");e.className="sort-order-icon",r.createChild("div","sort-order-icon-container").appendChild(e)}}addColumn(e,t){this.innerAddColumn(e,t)}innerRemoveColumn(e){if(!this.columns[e])return;delete this.columns[e];const t=this.columnsArray.findIndex((t=>t.id===e));this.columnsArray.splice(t,1);const i=this.dataTableHeaders[e];i.parentElement&&i.parentElement.removeChild(i),delete this.dataTableHeaders[e]}removeColumn(e){this.innerRemoveColumn(e)}setCellClass(e){this.cellClass=e}refreshHeader(){this.dataTableColumnGroup.removeChildren(),this.headerRow.removeChildren(),this.topFillerRow.removeChildren(),this.bottomFillerRow.removeChildren();for(let e=0;e<this.visibleColumnsArray.length;++e){const t=this.visibleColumnsArray[e],i=t.id,s=this.dataTableColumnGroup.createChild("col");t.width&&(s.style.width=t.width),this.headerRow.appendChild(this.dataTableHeaders[i]);const n=this.topFillerRow.createChild("th","top-filler-td");n.textContent=t.title||null,n.scope="col";const r=this.bottomFillerRow.createChild("td","bottom-filler-td");c.set(r,i)}const e=this.headerRow.createChild("th","corner");n.ARIAUtils.setHidden(e,!0);const t=this.topFillerRow.createChild("th","corner");t.classList.add("top-filler-td"),t.scope="col",this.bottomFillerRow.createChild("td","corner").classList.add("bottom-filler-td"),this.dataTableColumnGroup.createChild("col","corner")}setVerticalPadding(e,t,i=!1){const s=e+"px",n=e||t?t+"px":"auto";this.topFillerRow.style.height===s&&this.bottomFillerRow.style.height===n||(this.topFillerRow.style.height=s,this.bottomFillerRow.style.height=n,i||this.dispatchEventToListeners("PaddingChanged"))}setRootNode(e){this.rootNodeInternal&&(this.rootNodeInternal.removeChildren(),this.rootNodeInternal.dataGrid=null,this.rootNodeInternal.isRoot=!1),this.rootNodeInternal=e,e.isRoot=!0,e.setHasChildren(!1),e.expandedInternal=!0,e.revealedInternal=!0,e.selectable=!1,e.dataGrid=this}rootNode(){let e=this.rootNodeInternal;return e||(e=new N,this.setRootNode(e)),e}ondblclick(e){if(this.editing||this.editingNode)return;const t=this.columnIdFromNode(e.target);t&&this.columns[t].editable&&this.startEditing(e.target)}startEditingColumnOfDataGridNode(e,t){this.editing=!0,this.editingNode=e,this.editingNode.select();const i=this.editingNode.element();if(!i)return;const s=i.children[t],r=h.get(s);r&&(s.textContent=r);const l=this.visibleColumnsArray[t];if("Boolean"===l.dataType){const t=n.UIUtils.CheckboxLabel.create(void 0,e.data[l.id]);n.ARIAUtils.setLabel(t,l.title||"");let i=!1;t.style.height="100%";const r=t.checkboxElement;r.classList.add("inside-datagrid");const d=r.checked;r.addEventListener("change",(()=>{i=!0,this.editingCommitted(s,r.checked,d,void 0,"forward")}),!1),r.addEventListener("keydown",(e=>{if("Tab"===e.key)return e.consume(!0),i=!0,this.editingCommitted(s,r.checked,d,void 0,e.shiftKey?"backward":"forward");" "===e.key?(e.consume(!0),r.checked=!r.checked):"Enter"===e.key&&(e.consume(!0),i=!0,this.editingCommitted(s,r.checked,d,void 0,"forward"))}),!1),r.addEventListener("blur",(()=>{i||this.editingCommitted(s,r.checked,r.checked,void 0,"next")}),!1),s.innerHTML="",s.appendChild(t),r.focus()}else{n.InplaceEditor.InplaceEditor.startEditing(s,this.startEditingConfig(s));const e=s.getComponentSelection();e&&e.selectAllChildren(s)}}startEditingNextEditableColumnOfDataGridNode(e,t,i){const s=this.columns[t],n=this.visibleColumnsArray.indexOf(s),r=this.nextEditableColumn(n,!1,i);-1!==r&&this.startEditingColumnOfDataGridNode(e,r)}startEditing(e){if(!n.UIUtils.enclosingNodeOrSelfWithNodeName(e,"td"))return;if(this.editingNode=this.dataGridNodeFromNode(e),!this.editingNode){if(!this.creationNode)return;this.editingNode=this.creationNode}if(this.editingNode instanceof x&&this.editingNode.isCreationNode)return void this.startEditingColumnOfDataGridNode(this.editingNode,this.nextEditableColumn(-1));const t=this.columnIdFromNode(e);if(!t)return;const i=this.columns[t],s=this.visibleColumnsArray.indexOf(i);this.editingNode&&this.startEditingColumnOfDataGridNode(this.editingNode,s)}renderInline(){this.element.classList.add("inline"),this.cornerWidth=0,this.inline=!0,this.updateWidths()}startEditingConfig(e){return new n.InplaceEditor.Config(this.editingCommitted.bind(this),this.editingCancelled.bind(this))}editingCommitted(e,t,i,s,n){const r=this.columnIdFromNode(e);if(!r)return void this.editingCancelled(e);const l=this.columns[r],d=this.visibleColumnsArray.indexOf(l);if(!this.editingNode)return;const o=null===this.editingNode.data[r]?"":this.editingNode.data[r],a=this.editingNode;function h(e){if(n){if("forward"===n){const t=this.nextEditableColumn(-1),i=a instanceof x&&a.isCreationNode;if(i&&d===t&&!e)return;const s=this.nextEditableColumn(d);if(-1!==s)return void this.startEditingColumnOfDataGridNode(a,s);const n=a.traverseNextNode(!0,null,!0);return n?void this.startEditingColumnOfDataGridNode(n,t):i&&e&&this.creationNode?(this.addCreationNode(!1),void this.startEditingColumnOfDataGridNode(this.creationNode,t)):void 0}if("backward"!==n);else{const e=this.nextEditableColumn(d,!0);if(-1!==e)return void this.startEditingColumnOfDataGridNode(a,e);const t=this.nextEditableColumn(this.visibleColumnsArray.length,!0),i=a.traversePreviousNode(!0,!0);i&&this.startEditingColumnOfDataGridNode(i,t)}}}if(this.setElementContent(e,t),o===t)return this.editingCancelled(e),void h.call(this,!1);this.editingNode.data[r]=t,this.editCallback&&(this.editCallback(this.editingNode,r,o,t),this.editingNode instanceof x&&this.editingNode.isCreationNode&&this.addCreationNode(!1),this.editingCancelled(e),h.call(this,!0))}editingCancelled(e){this.editing=!1,this.editingNode=null}nextEditableColumn(e,t,i){const s=t?-1:1,n=i?e:e+s,r=this.visibleColumnsArray;for(let e=n;e>=0&&e<r.length;e+=s)if(r[e].editable)return e;return-1}sortColumnId(){return this.sortColumnCell&&c.get(this.sortColumnCell)||null}sortOrder(){return!this.sortColumnCell||this.sortColumnCell.classList.contains(b.Ascending)?b.Ascending:this.sortColumnCell.classList.contains(b.Descending)?b.Descending:null}isSortOrderAscending(){return!this.sortColumnCell||this.sortColumnCell.classList.contains(b.Ascending)}autoSizeWidths(e,t,i){t&&(t=Math.min(t,Math.floor(100/e.length)));let s=0;for(let t=0;t<e.length;++t)s+=e[t];let n=0;for(let r=0;r<e.length;++r){let l=Math.round(100*e[r]/s);t&&l<t?l=t:i&&l>i&&(l=i),n+=l,e[r]=l}let r=n-100;for(;t&&r>0;)for(let i=0;i<e.length&&(!(e[i]>t)||(--e[i],--r,r));++i);for(;i&&r<0;)for(let t=0;t<e.length&&(!(e[t]<i)||(++e[t],++r,r));++t);return e}autoSizeColumns(e,t,i){let s=[];for(let e=0;e<this.columnsArray.length;++e)s.push((this.columnsArray[e].title||"").length);if(i=i||0,!this.rootNodeInternal)return;const n=this.enumerateChildren(this.rootNodeInternal,[],i+1);for(let e=0;e<n.length;++e){const t=n[e];for(let e=0;e<this.columnsArray.length;++e){const i=String(t.data[this.columnsArray[e].id]);i.length>s[e]&&(s[e]=i.length)}}s=this.autoSizeWidths(s,e,t);for(let e=0;e<this.columnsArray.length;++e)this.columnsArray[e].weight=s[e];this.columnWidthsInitialized=!1,this.updateWidths()}enumerateChildren(e,t,i){if(e.isRoot||t.push(e),!i)return[];for(let s=0;s<e.children.length;++s)this.enumerateChildren(e.children[s],t,i-1);return t}onResize(){this.updateWidths()}updateWidths(){if(!this.columnWidthsInitialized&&this.element.offsetWidth){const e=this.element.offsetWidth-this.cornerWidth,t=this.dataTableHeadInternal.rows[0].cells.length-1;for(let i=0;i<t;i++){const t=this.visibleColumnsArray[i];t.weight||(t.weight=100*this.getPreferredWidth(i)/e||10)}this.columnWidthsInitialized=!0}this.applyColumnWeights()}indexOfVisibleColumn(e){return this.visibleColumnsArray.findIndex((t=>t.id===e))}setName(t){this.columnWeightsSetting=e.Settings.Settings.instance().createSetting("data-grid-"+t+"-column-weights",{}),this.loadColumnWeights()}resetColumnWeights(){for(const e of this.columnsArray)e.defaultWeight&&(e.weight=e.defaultWeight);this.applyColumnWeights(),this.saveColumnWeights()}loadColumnWeights(){if(!this.columnWeightsSetting)return;const e=this.columnWeightsSetting.get();for(let t=0;t<this.columnsArray.length;++t){const i=this.columnsArray[t],s=e[i.id];s&&(i.weight=s)}this.applyColumnWeights()}saveColumnWeights(){if(!this.columnWeightsSetting)return;const e={};for(let t=0;t<this.columnsArray.length;++t){const i=this.columnsArray[t];e[i.id]=i.weight}this.columnWeightsSetting.set(e)}wasShown(){this.loadColumnWeights()}willHide(){}getPreferredWidth(e){return u.get(this.dataTableColumnGroup.children[e])||this.dataTableHeadInternal.rows[0].cells[e].offsetWidth}applyColumnWeights(){let e=this.element.offsetWidth-this.cornerWidth;if(e<=0)return;let t=0;const i=[];for(let s=0;s<this.visibleColumnsArray.length;++s){if(this.visibleColumnsArray[s].fixedWidth){const t=this.getPreferredWidth(s);i[s]=t,e-=t}else t+=this.visibleColumnsArray[s].weight||0}let s=0,n=0;for(let r=0;r<this.visibleColumnsArray.length;++r){const l=this.visibleColumnsArray[r];let d;if(l.fixedWidth)d=i[r];else{s+=l.weight||0;const i=s*e/t|0;d=Math.max(i-n,14),n=i}this.setPreferredWidth(r,d)}this.positionResizers()}setColumnsVisibility(e){this.visibleColumnsArray=[];for(const t of this.columnsArray)e.has(t.id)&&this.visibleColumnsArray.push(t);this.refreshHeader(),this.applyColumnWeights();const t=this.enumerateChildren(this.rootNode(),[],-1);for(const e of t)e.refresh()}get scrollContainer(){return this.scrollContainerInternal}positionResizers(){const e=this.dataTableColumnGroup.children.length-1,t=[],i=this.resizers;for(;i.length>e-1;){const e=i.pop();e&&e.remove()}for(let i=0;i<e-1;i++)t[i]=(t[i-1]||0)+this.dataTableHeadInternal.rows[0].cells[i].offsetWidth;for(let s=0;s<e-1;s++){let e=i[s];e||(e=document.createElement("div"),p.set(e,s),e.classList.add("data-grid-resizer"),n.UIUtils.installDragHandle(e,this.startResizerDragging.bind(this),this.resizerDragging.bind(this),this.endResizerDragging.bind(this),"col-resize"),this.element.appendChild(e),i.push(e)),g.get(e)!==t[s]&&(g.set(e,t[s]),e.style.left=t[s]+"px")}}addCreationNode(e){this.creationNode&&this.creationNode.makeNormal();const t={};for(const e in this.columns)t[e]=null;this.creationNode=new x(t,e),n.ARIAUtils.alert(a(d.emptyRowCreated)),this.rootNode().appendChild(this.creationNode)}keyDown(e){if(!(e instanceof KeyboardEvent))return;if(this.selectedNode&&this.selectedNode.element().tabIndex<0&&s.logKeyDown(this.selectedNode.element(),e),e.shiftKey||e.metaKey||e.ctrlKey||this.editing||n.UIUtils.isEditing())return;let t,i=!1;if(this.selectedNode)if("ArrowUp"!==e.key||e.altKey)if("ArrowDown"!==e.key||e.altKey){if("ArrowLeft"===e.key)this.selectedNode.expanded?(e.altKey?this.selectedNode.collapseRecursively():this.selectedNode.collapse(),i=!0):this.selectedNode.parent&&!this.selectedNode.parent.isRoot&&(i=!0,this.selectedNode.parent.selectable?(t=this.selectedNode.parent,i=!!t):this.selectedNode.parent&&this.selectedNode.parent.collapse());else if("ArrowRight"===e.key)this.selectedNode.revealed?this.selectedNode.hasChildren()&&(i=!0,this.selectedNode.expanded?(t=this.selectedNode.children[0],i=!!t):e.altKey?this.selectedNode.expandRecursively():this.selectedNode.expand()):(this.selectedNode.reveal(),i=!0);else if(8===e.keyCode||46===e.keyCode)this.deleteCallback&&(i=!0,this.deleteCallback(this.selectedNode));else if("Enter"===e.key)if(this.editCallback){i=!0;const e=this.selectedNode.element();if(!e)return;this.startEditing(e.children[this.nextEditableColumn(-1)])}else this.dispatchEventToListeners("OpenedNode",this.selectedNode)}else{for(t=this.selectedNode.traverseNextNode(!0);t&&!t.selectable;)t=t.traverseNextNode(!0);i=!!t}else{for(t=this.selectedNode.traversePreviousNode(!0);t&&!t.selectable;)t=t.traversePreviousNode(!0);i=!!t}else"ArrowUp"!==e.key||e.altKey?"ArrowDown"!==e.key||e.altKey||(t=this.firstSelectableNode()):t=this.lastSelectableNode(),i=!!t;t&&(t.reveal(),t.select()),i&&this.element!==document.activeElement&&!this.element.contains(document.activeElement)&&this.element.focus(),i&&e.consume(!0)}updateSelectionBeforeRemoval(e,t){let i,s=this.selectedNode;for(;s&&s!==e;)s=s.parent;if(!s)return;for(s=e;s&&!s.nextSibling;s=s.parent);for(s&&(i=s.nextSibling);i&&!i.selectable;)i=i.traverseNextNode(!0);const n=i instanceof x&&i.isCreationNode;if(!i||n){if(!e)return;for(i=e.traversePreviousNode(!0);i&&!i.selectable;)i=i.traversePreviousNode(!0)}i?(i.reveal(),i.select()):this.selectedNode&&this.selectedNode.deselect()}dataGridNodeFromNode(e){const t=n.UIUtils.enclosingNodeOrSelfWithNodeName(e,"tr");return t&&this.elementToDataGridNode.get(t)||null}columnIdFromNode(e){const t=n.UIUtils.enclosingNodeOrSelfWithNodeName(e,"td");return t&&c.get(t)||null}clickInHeaderCell(e){const t=n.UIUtils.enclosingNodeOrSelfWithNodeName(e.target,"th");t&&this.sortByColumnHeaderCell(t)}sortByColumnHeaderCell(e){if(!c.has(e)||!e.classList.contains("sortable"))return;let t=b.Ascending;e===this.sortColumnCell&&this.isSortOrderAscending()&&(t=b.Descending),this.sortColumnCell&&this.sortColumnCell.classList.remove(b.Ascending,b.Descending),this.sortColumnCell=e,e.classList.add(t),this.dispatchEventToListeners("SortingChanged")}markColumnAsSortedBy(e,t){this.sortColumnCell&&this.sortColumnCell.classList.remove(b.Ascending,b.Descending),this.sortColumnCell=this.dataTableHeaders[e],this.sortColumnCell.classList.add(t)}headerTableHeader(e){return this.dataTableHeaders[e]}mouseDownInDataTable(e){const t=e.target,i=this.dataGridNodeFromNode(t);if(!i||!i.selectable||i.isEventWithinDisclosureTriangle(e))return;const s=this.columnIdFromNode(t);s&&this.columns[s].nonSelectable||(e.metaKey?i.selected?i.deselect():i.select():(i.select(),this.dispatchEventToListeners("OpenedNode",i)))}setHeaderContextMenuCallback(e){this.headerContextMenuCallback=e}setRowContextMenuCallback(e){this.rowContextMenuCallback=e}contextMenu(e){if(!(e instanceof MouseEvent))return;const t=new n.ContextMenu.ContextMenu(e),s=e.target,r=this.visibleColumnsArray.filter((e=>e.sortable&&e.title)),l=this.columnsArray.filter((e=>-1===r.indexOf(e)&&e.allowInSortByEvenWhenHidden)),o=[...r,...l];if(o.length>0){const e=t.defaultSection().appendSubMenuItem(a(d.sortByString),!1,"sort-by");for(const t of o){const s=this.dataTableHeaders[t.id];e.defaultSection().appendItem(t.title,this.sortByColumnHeaderCell.bind(this,s),{jslogContext:i.StringUtilities.toKebabCase(t.id)})}}if(s.isSelfOrDescendant(this.dataTableHeadInternal))return this.headerContextMenuCallback&&this.headerContextMenuCallback(t),t.defaultSection().appendItem(a(d.resetColumns),this.resetColumnWeights.bind(this),{jslogContext:"reset-columns"}),void t.show();const h=t.defaultSection().appendSubMenuItem(a(d.headerOptions),!1,"header-options");this.headerContextMenuCallback&&this.headerContextMenuCallback(h),h.defaultSection().appendItem(a(d.resetColumns),this.resetColumnWeights.bind(this),{jslogContext:"reset-columns"});const c=0===e.button,u=c?this.selectedNode:this.dataGridNodeFromNode(s),g=this.selectedNode&&this.selectedNode.existingElement();if(c&&g){const e=g.getBoundingClientRect();if(e){const i=(e.right+e.left)/2,s=(e.bottom+e.top)/2;t.setX(i),t.setY(s)}}if(!this.refreshCallback||u&&u===this.creationNode||t.defaultSection().appendItem(a(d.refresh),this.refreshCallback.bind(this),{jslogContext:"refresh"}),u&&u.selectable&&!u.isEventWithinDisclosureTriangle(e)){if(this.editCallback)if(u===this.creationNode){const e=this.nextEditableColumn(-1),i=u.element().children[e];t.defaultSection().appendItem(a(d.addNew),this.startEditing.bind(this,i),{jslogContext:"add-new"})}else if(c){const e=this.nextEditableColumn(-1);if(e>-1){const i=this.visibleColumnsArray[e];i&&i.editable&&t.defaultSection().appendItem(a(d.editS,{PH1:String(i.title)}),this.startEditingColumnOfDataGridNode.bind(this,u,e),{jslogContext:"edit"})}}else{const e=this.columnIdFromNode(s);e&&this.columns[e].editable&&t.defaultSection().appendItem(a(d.editS,{PH1:String(this.columns[e].title)}),this.startEditing.bind(this,s),{jslogContext:"edit"})}this.deleteCallback&&u!==this.creationNode&&t.defaultSection().appendItem(a(d.delete),this.deleteCallback.bind(this,u),{jslogContext:"delete"}),this.rowContextMenuCallback&&this.rowContextMenuCallback(t,u)}t.show()}clickInDataTable(e){const t=this.dataGridNodeFromNode(e.target);t&&t.hasChildren()&&t.isEventWithinDisclosureTriangle(e)&&(t.expanded?e.altKey?t.collapseRecursively():t.collapse():e.altKey?t.expandRecursively():t.expand())}setResizeMethod(e){this.resizeMethod=e}startResizerDragging(e){return this.currentResizer=e.target,!0}endResizerDragging(){this.currentResizer=null,this.saveColumnWeights()}resizerDragging(e){const t=this.currentResizer;if(!t)return;let s=e.clientX-this.element.getBoundingClientRect().left,n=0,r=p.get(t);if(void 0===r)return;let l=r+1;for(let e=0;e<r;e++)n+=this.getPreferredWidth(e);"last"===this.resizeMethod?l=this.resizers.length:"first"===this.resizeMethod&&(n+=this.getPreferredWidth(r)-this.getPreferredWidth(0),r=0);const d=n+this.getPreferredWidth(r)+this.getPreferredWidth(l),o=n+C,a=d-C;if(o>a)return;s=i.NumberUtilities.clamp(s,o,a);const h=s-v;g.set(t,h),t.style.left=h+"px",this.setPreferredWidth(r,s-n),this.setPreferredWidth(l,d-s);const c=this.visibleColumnsArray[r],u=this.visibleColumnsArray[l];if(c.weight&&u.weight){const e=c.weight+u.weight,t=d-n;c.weight=(s-n)*e/t,u.weight=(d-s)*e/t}this.positionResizers(),this.updateWidths(),e.preventDefault()}setPreferredWidth(e,t){const i=this.dataTableColumnGroup.children[e];u.set(i,t),i.style.width=t+"px"}columnOffset(e){if(!this.element.offsetWidth)return 0;for(let t=1;t<this.visibleColumnsArray.length;++t)if(e===this.visibleColumnsArray[t].id&&this.resizers[t-1])return g.get(this.resizers[t-1])||0;return 0}asWidget(){return this.dataGridWidget||(this.dataGridWidget=new w(this)),this.dataGridWidget}topFillerRowElement(){return this.topFillerRow}headerHeightInScroller(){return this.scrollContainer===this.dataContainerInternal?this.headerHeight():0}headerHeight(){return this.dataTableHeadInternal.offsetHeight}revealNode(e){e.scrollIntoViewIfNeeded(!1),e.offsetTop-this.scrollContainer.scrollTop<this.headerHeight()&&(this.scrollContainer.scrollTop=e.offsetTop-this.headerHeight())}}const f=14;var b;!function(e){e.Ascending="sort-ascending",e.Descending="sort-descending"}(b||(b={}));const C=34,v=3;class N{elementInternal;expandedInternal;selectedInternal;dirty;inactive;key;depthInternal;revealedInternal;attachedInternal;savedPosition;shouldRefreshChildrenInternal;dataInternal;hasChildrenInternal;children;dataGrid;parent;previousSibling;nextSibling;#e=15;selectable;isRoot;nodeAccessibleText;cellAccessibleTextMap;isCreationNode;constructor(e,t){this.elementInternal=null,this.expandedInternal=!1,this.selectedInternal=!1,this.dirty=!1,this.inactive=!1,this.attachedInternal=!1,this.savedPosition=null,this.shouldRefreshChildrenInternal=!0,this.dataInternal=e||{},this.hasChildrenInternal=t||!1,this.children=[],this.dataGrid=null,this.parent=null,this.previousSibling=null,this.nextSibling=null,this.selectable=!0,this.isRoot=!1,this.nodeAccessibleText="",this.cellAccessibleTextMap=new Map,this.isCreationNode=!1}element(){if(!this.elementInternal){const e=this.createElement();this.createCells(e)}return this.elementInternal}createElement(){return this.elementInternal=document.createElement("tr"),this.elementInternal.setAttribute("jslog",`${s.tableRow().track({keydown:"ArrowUp|ArrowDown|ArrowLeft|ArrowRight|Enter|Space"})}`),this.elementInternal.classList.add("data-grid-data-grid-node"),this.dataGrid&&this.dataGrid.elementToDataGridNode.set(this.elementInternal,this),this.hasChildrenInternal&&this.elementInternal.classList.add("parent"),this.expanded&&this.elementInternal.classList.add("expanded"),this.selected&&this.elementInternal.classList.add("selected"),this.revealed&&this.elementInternal.classList.add("revealed"),this.dirty&&this.elementInternal.classList.add("dirty"),this.inactive&&this.elementInternal.classList.add("inactive"),this.isCreationNode&&this.elementInternal.classList.add("creation-node"),this.elementInternal}existingElement(){return this.elementInternal||null}resetElement(){this.elementInternal=null}createCells(e){if(e.removeChildren(),!this.dataGrid||!this.parent)return;const t=this.dataGrid.visibleColumnsArray,i=[];!this.hasChildrenInternal&&this.parent.isRoot||i.push(a(d.levelS,{PH1:this.depth+1}));for(let s=0;s<t.length;++s){const n=t[s],r=e.appendChild(this.createCell(n.id));"Boolean"===n.dataType&&!0===this.data[n.id]&&this.setCellAccessibleName(a(d.checked),r,n.id),i.push(`${n.title}: ${this.cellAccessibleTextMap.get(n.id)||r.textContent}`)}this.nodeAccessibleText=i.join(", ");const s=this.createTDWithClass("corner");n.ARIAUtils.setHidden(s,!0),e.appendChild(s)}get data(){return this.dataInternal}set data(e){this.dataInternal=e||{},this.refresh()}get revealed(){if(void 0!==this.revealedInternal)return this.revealedInternal;let e=this.parent;for(;e&&!e.isRoot;){if(!e.expanded)return this.revealedInternal=!1,!1;e=e.parent}return this.revealed=!0,!0}set revealed(e){if(this.revealedInternal!==e){this.revealedInternal=e,this.elementInternal&&this.elementInternal.classList.toggle("revealed",this.revealedInternal);for(let t=0;t<this.children.length;++t)this.children[t].revealed=e&&this.expanded}}isDirty(){return this.dirty}setDirty(e){this.dirty!==e&&(this.dirty=e,this.elementInternal&&(e?this.elementInternal.classList.add("dirty"):this.elementInternal.classList.remove("dirty")))}isInactive(){return this.inactive}setInactive(e){this.inactive!==e&&(this.inactive=e,this.elementInternal&&(e?this.elementInternal.classList.add("inactive"):this.elementInternal.classList.remove("inactive")))}hasChildren(){return this.hasChildrenInternal}setHasChildren(e){this.hasChildrenInternal!==e&&(this.hasChildrenInternal=e,this.elementInternal&&(this.elementInternal.classList.toggle("parent",this.hasChildrenInternal),this.elementInternal.classList.toggle("expanded",this.hasChildrenInternal&&this.expanded)))}get depth(){return void 0!==this.depthInternal||(this.parent&&!this.parent.isRoot?this.depthInternal=this.parent.depth+1:this.depthInternal=0),this.depthInternal}get leftPadding(){return this.depth*(this.dataGrid?this.dataGrid.indentWidth:1)}get shouldRefreshChildren(){return this.shouldRefreshChildrenInternal}set shouldRefreshChildren(e){this.shouldRefreshChildrenInternal=e,e&&this.expanded&&this.expand()}get selected(){return this.selectedInternal}set selected(e){e?this.select():this.deselect()}get expanded(){return this.expandedInternal}set expanded(e){e?this.expand():this.collapse()}refresh(){this.dataGrid||(this.elementInternal=null),this.elementInternal&&this.createCells(this.elementInternal)}createTDWithClass(e){const t=document.createElement("td");e&&(t.className=e);const i=this.dataGrid?this.dataGrid.cellClass:null;return i&&t.classList.add(i),t}createTD(e){const t=this.createTDWithClass(e+"-column");if(c.set(t,e),this.dataGrid){const n=this.dataGrid.columns[e].editable;t.setAttribute("jslog",`${s.tableCell().track({click:!0,keydown:!!n&&"Enter|Space|Escape",dblclick:n,change:n}).context(i.StringUtilities.toKebabCase(e))}`);const r=this.dataGrid.columns[e].align;r&&t.classList.add(r),e===this.dataGrid.disclosureColumnId&&(t.classList.add("disclosure"),this.leftPadding&&t.style.setProperty("padding-left",this.leftPadding+"px")),n&&(t.tabIndex=0,t.ariaHasPopup="true")}return t}createCell(e){const t=this.createTD(e),i=this.data[e];return i instanceof Node?t.appendChild(i):null!==i&&this.dataGrid&&this.dataGrid.setElementContent(t,i),t}setCellAccessibleName(e,t,i){this.cellAccessibleTextMap.set(i,e);for(let e=0;e<t.children.length;e++)n.ARIAUtils.markAsHidden(t.children[e]);n.ARIAUtils.setLabel(t,e)}nodeSelfHeight(){return 20}appendChild(e){this.insertChild(e,this.children.length)}resetNode(e){delete this.depthInternal,delete this.revealedInternal,e||(this.previousSibling&&(this.previousSibling.nextSibling=this.nextSibling),this.nextSibling&&(this.nextSibling.previousSibling=this.previousSibling),this.dataGrid=null,this.parent=null,this.nextSibling=null,this.previousSibling=null,this.attachedInternal=!1)}insertChild(e,t){if(!e)throw"insertChild: Node can't be undefined or null.";if(e.parent===this){const i=this.children.indexOf(e);if(i<0&&console.assert(!1,"Inconsistent DataGrid state"),i===t)return;i<t&&--t}e.remove(),this.children.splice(t,0,e),this.setHasChildren(!0),e.parent=this,e.dataGrid=this.dataGrid,e.recalculateSiblings(t),e.shouldRefreshChildrenInternal=!0;let i=e.children[0];for(;i;)i.resetNode(!0),i.dataGrid=this.dataGrid,i.attachedInternal=!1,i.shouldRefreshChildrenInternal=!0,i=i.traverseNextNode(!1,e,!0);this.expanded&&e.attach(),this.revealed||(e.revealed=!1)}remove(){this.parent&&this.parent.removeChild(this)}removeChild(e){if(!e)throw"removeChild: Node can't be undefined or null.";if(e.parent!==this)throw"removeChild: Node is not a child of this node.";this.dataGrid&&this.dataGrid.updateSelectionBeforeRemoval(e,!1),e.detach(),e.resetNode(),i.ArrayUtilities.removeElement(this.children,e,!0),this.children.length<=0&&this.setHasChildren(!1)}removeChildren(){this.dataGrid&&this.dataGrid.updateSelectionBeforeRemoval(this,!0);for(let e=0;e<this.children.length;++e){const t=this.children[e];t.detach(),t.resetNode()}this.children=[],this.setHasChildren(!1)}recalculateSiblings(e){if(!this.parent)return;const t=this.parent.children[e-1]||null;t&&(t.nextSibling=this),this.previousSibling=t;const i=this.parent.children[e+1]||null;i&&(i.previousSibling=this),this.nextSibling=i}collapse(){if(!this.isRoot){this.elementInternal&&this.elementInternal.classList.remove("expanded"),this.expandedInternal=!1,this.selected&&this.dataGrid&&this.dataGrid.announceSelectedGridNode();for(let e=0;e<this.children.length;++e)this.children[e].revealed=!1}}collapseRecursively(){let e=this;for(;e;)e.expanded&&e.collapse(),e=e.traverseNextNode(!1,this,!0)}populate(){}expand(){if(this.hasChildrenInternal&&!this.expandedInternal&&!this.isRoot){if(this.revealed&&!this.shouldRefreshChildrenInternal)for(let e=0;e<this.children.length;++e)this.children[e].revealed=!0;if(this.shouldRefreshChildrenInternal){for(let e=0;e<this.children.length;++e)this.children[e].detach();if(this.populate(),this.attachedInternal)for(let e=0;e<this.children.length;++e){const t=this.children[e];this.revealed&&(t.revealed=!0),t.attach()}this.shouldRefreshChildrenInternal=!1}this.elementInternal&&this.elementInternal.classList.add("expanded"),this.selected&&this.dataGrid&&this.dataGrid.announceSelectedGridNode(),this.expandedInternal=!0}}expandRecursively(){let e=this;for(;e;)e.expand(),e=e.traverseNextNode(!1,this)}reveal(){if(this.isRoot||!this.dataGrid)return;let e=this.parent;for(;e&&!e.isRoot;)e.expanded||e.expand(),e=e.parent;this.dataGrid.revealNode(this.element())}select(e){this.dataGrid&&this.selectable&&!this.selected&&(this.dataGrid.selectedNode&&this.dataGrid.selectedNode.deselect(),this.selectedInternal=!0,this.dataGrid.selectedNode=this,this.elementInternal&&(this.elementInternal.classList.add("selected"),this.elementInternal.focus(),this.dataGrid.setHasSelection(!0),this.dataGrid.announceSelectedGridNode()),e||this.dataGrid.dispatchEventToListeners("SelectedNode",this))}revealAndSelect(){this.isRoot||(this.reveal(),this.select())}deselect(e){this.dataGrid&&this.dataGrid.selectedNode===this&&this.selected&&(this.selectedInternal=!1,this.dataGrid.selectedNode=null,this.elementInternal&&(this.elementInternal.classList.remove("selected"),this.dataGrid.setHasSelection(!1)),e||this.dataGrid.dispatchEventToListeners("DeselectedNode"))}traverseNextNode(e,t,i,s){!i&&this.hasChildrenInternal&&this.populate(),s&&(s.depthChange=0);let n=!e||this.revealed?this.children[0]:null;if(n&&(!e||this.expanded))return s&&(s.depthChange=1),n;if(this===t)return null;if(n=!e||this.revealed?this.nextSibling:null,n)return n;for(n=this;n&&!n.isRoot&&(e&&!n.revealed||!n.nextSibling)&&n.parent!==t;)s&&(s.depthChange-=1),n=n.parent;return n&&(!e||n.revealed)?n.nextSibling:null}traversePreviousNode(e,t){let i=!e||this.revealed?this.previousSibling:null;for(!t&&i&&i.hasChildrenInternal&&i.populate();i&&(!e||i.revealed&&i.expanded)&&i.children[i.children.length-1];)!t&&i.hasChildrenInternal&&i.populate(),i=!e||i.revealed&&i.expanded?i.children[i.children.length-1]:null;return i||(!this.parent||this.parent.isRoot?null:this.parent)}isEventWithinDisclosureTriangle(e){if(!this.hasChildrenInternal)return!1;const t=n.UIUtils.enclosingNodeOrSelfWithNodeName(e.target,"td");if(!(t&&t instanceof HTMLElement&&t.classList.contains("disclosure")))return!1;const i=t.getBoundingClientRect().left+this.leftPadding;return e.pageX>=i&&e.pageX<=i+this.#e}attach(){if(!this.dataGrid||this.attachedInternal)return;this.attachedInternal=!0;const e=this.traversePreviousNode(!0,!0),t=e?e.element():this.dataGrid.topFillerRow;if(this.dataGrid.dataTableBody.insertBefore(this.element(),t.nextSibling),this.expandedInternal)for(let e=0;e<this.children.length;++e)this.children[e].attach()}detach(){if(this.attachedInternal){this.attachedInternal=!1,this.elementInternal&&this.elementInternal.remove();for(let e=0;e<this.children.length;++e)this.children[e].detach()}}savePosition(){if(!this.savedPosition){if(!this.parent)throw"savePosition: Node must have a parent.";this.savedPosition={parent:this.parent,index:this.parent.children.indexOf(this)}}}restorePosition(){this.savedPosition&&(this.parent!==this.savedPosition.parent&&this.savedPosition.parent.insertChild(this,this.savedPosition.index),this.savedPosition=null)}}class x extends N{isCreationNode;constructor(e,t){super(e,t),this.isCreationNode=!0}makeNormal(){this.isCreationNode=!1}}class w extends n.Widget.VBox{dataGrid;constructor(e){super(),this.dataGrid=e,this.element.appendChild(e.element),this.setDefaultFocusedElement(e.element)}wasShown(){this.registerCSSFiles([l]),this.dataGrid.wasShown()}willHide(){this.dataGrid.willHide()}onResize(){this.dataGrid.onResize()}elementsToRestoreScrollPositionsFor(){return[this.dataGrid.scrollContainer]}}var I=Object.freeze({__proto__:null,DataGridImpl:m,CornerWidth:f,get Order(){return b},ColumnResizePadding:C,CenterResizerOverBorderAdjustment:v,DataGridNode:N,CreationDataGridNode:x,DataGridWidget:w});const y=r.RenderCoordinator.RenderCoordinator.instance();class S extends(e.ObjectWrapper.eventMixin(m)){onScrollBound;visibleNodes;stickToBottom;updateIsFromUser;lastScrollTop;firstVisibleIsStriped;isStriped;constructor(e){super(e),this.onScrollBound=this.onScroll.bind(this),this.scrollContainer.addEventListener("scroll",this.onScrollBound,!0),this.visibleNodes=[],this.inline=!1,this.stickToBottom=!1,this.updateIsFromUser=!1,this.lastScrollTop=0,this.firstVisibleIsStriped=!1,this.isStriped=!1,this.setRootNode(new k)}setStriped(e){this.isStriped=e;let t=!0;if(this.visibleNodes.length){const e=this.rootNode().flatChildren();t=Boolean(e.indexOf(this.visibleNodes[0]))}this.updateStripesClass(t)}updateStripesClass(e){this.element.classList.toggle("striped-data-grid",!e&&this.isStriped),this.element.classList.toggle("striped-data-grid-starts-with-odd",e&&this.isStriped)}setScrollContainer(e){this.scrollContainer.removeEventListener("scroll",this.onScrollBound,!0),this.scrollContainerInternal=e,this.scrollContainer.addEventListener("scroll",this.onScrollBound,!0)}onResize(){this.stickToBottom&&(this.scrollContainer.scrollTop=this.scrollContainer.scrollHeight-this.scrollContainer.clientHeight),this.scheduleUpdate(),super.onResize()}setStickToBottom(e){this.stickToBottom=e}onScroll(e){this.lastScrollTop!==this.scrollContainer.scrollTop&&this.scheduleUpdate(!0)}scheduleUpdateStructure(){this.scheduleUpdate()}scheduleUpdate(e){this.updateIsFromUser=this.updateIsFromUser||Boolean(e),y.write("ViewportDataGrid.render",this.update.bind(this))}updateInstantly(){this.update()}renderInline(){this.inline=!0,super.renderInline(),this.update()}calculateVisibleNodes(e,t){const i=this.rootNode().flatChildren();if(this.inline)return{topPadding:0,bottomPadding:0,contentHeight:0,visibleNodes:i,offset:0};const s=i.length;let n=0,r=0;for(;n<s&&r+i[n].nodeSelfHeight()<t;++n)r+=i[n].nodeSelfHeight();const l=n,d=r;for(;n<s&&r<t+e;++n)r+=i[n].nodeSelfHeight();const o=n;let a=0;for(;n<s;++n)a+=i[n].nodeSelfHeight();return this.stickToBottom=o===i.length,{topPadding:d,bottomPadding:a,contentHeight:r-d,visibleNodes:i.slice(l,o),offset:l}}contentHeight(){const e=this.rootNode().flatChildren();let t=0;for(let i=0,s=e.length;i<s;++i)t+=e[i].nodeSelfHeight();return t}update(){const e=this.scrollContainer.clientHeight-this.headerHeightInScroller();let t=this.scrollContainer.scrollTop;const i=t,s=Math.max(0,this.contentHeight()-e);!this.updateIsFromUser&&this.stickToBottom&&(t=s),this.updateIsFromUser=!1,t=Math.min(s,t);const n=this.calculateVisibleNodes(e,t),r=n.visibleNodes,l=new Set(r);for(let e=0;e<this.visibleNodes.length;++e){const t=this.visibleNodes[e];if(!l.has(t)&&t.attached()){const e=t.existingElement();e&&e.remove()}}let d=this.topFillerRowElement();const o=this.dataTableBody;let a=n.offset;if(r.length){const e=this.rootNode().flatChildren().indexOf(r[0]);this.updateStripesClass(Boolean(e%2)),this.stickToBottom&&-1!==e&&Boolean(e%2)!==this.firstVisibleIsStriped&&(a+=1)}this.firstVisibleIsStriped=Boolean(a%2);for(let e=0;e<r.length;++e){const t=r[e],i=t.element();t.setStriped((a+e)%2==0),i!==d.nextSibling&&o.insertBefore(i,d.nextSibling),t.revealed=!0,d=i}this.setVerticalPadding(n.topPadding,n.bottomPadding),this.lastScrollTop=t,t!==i&&(this.scrollContainer.scrollTop=t);const h=n.contentHeight<=e&&n.topPadding+n.bottomPadding===0;h!==this.element.classList.contains("data-grid-fits-viewport")&&(this.element.classList.toggle("data-grid-fits-viewport",h),this.updateWidths()),this.visibleNodes=r,this.dispatchEventToListeners("ViewportCalculated")}revealViewportNode(e){const t=this.rootNode().flatChildren(),i=t.indexOf(e);if(-1===i)return;let s=0;for(let e=0;e<i;++e)s+=t[e].nodeSelfHeight();const n=s+e.nodeSelfHeight();let r=this.scrollContainer.scrollTop;const l=this.scrollContainer.offsetHeight-this.headerHeightInScroller();r>s?(r=s,this.stickToBottom=!1):r+l<n&&(r=n-l),this.scrollContainer.scrollTop=r}}class k extends N{stale;flatNodes;isStripedInternal;constructor(e,t){super(e,t),this.stale=!1,this.flatNodes=null,this.isStripedInternal=!1}element(){const e=this.existingElement(),t=e||this.createElement();return e&&!this.stale||(this.createCells(t),this.stale=!1),t}setStriped(e){this.isStripedInternal=e,this.element().classList.toggle("odd",e)}isStriped(){return this.isStripedInternal}clearFlatNodes(){this.flatNodes=null;const e=this.parent;e&&e.clearFlatNodes()}flatChildren(){if(this.flatNodes)return this.flatNodes;const e=[],t=[this.children],i=[0];let s=0;for(;s>=0;){if(t[s].length<=i[s]){s--;continue}const n=t[s][i[s]++];e.push(n),n.expanded&&n.children.length&&(s++,t[s]=n.children,i[s]=0)}return this.flatNodes=e,e}insertChild(e,t){if(this.clearFlatNodes(),e.parent===this){const i=this.children.indexOf(e);if(i<0&&console.assert(!1,"Inconsistent DataGrid state"),i===t)return;i<t&&--t}e.remove(),e.parent=this,e.dataGrid=this.dataGrid,this.children.length||this.setHasChildren(!0),this.children.splice(t,0,e),e.recalculateSiblings(t),this.expanded&&this.dataGrid&&this.dataGrid.scheduleUpdateStructure()}removeChild(e){if(this.clearFlatNodes(),this.dataGrid&&this.dataGrid.updateSelectionBeforeRemoval(e,!1),e.previousSibling&&(e.previousSibling.nextSibling=e.nextSibling),e.nextSibling&&(e.nextSibling.previousSibling=e.previousSibling),e.parent!==this)throw"removeChild: Node is not a child of this node.";i.ArrayUtilities.removeElement(this.children,e,!0),e.unlink(),this.children.length||this.setHasChildren(!1),this.expanded&&this.dataGrid&&this.dataGrid.scheduleUpdateStructure()}removeChildren(){this.clearFlatNodes(),this.dataGrid&&this.dataGrid.updateSelectionBeforeRemoval(this,!0);for(let e=0;e<this.children.length;++e)this.children[e].unlink();this.children=[],this.expanded&&this.dataGrid&&this.dataGrid.scheduleUpdateStructure()}unlink(){const e=this.existingElement();this.attached()&&e&&e.remove(),this.resetNode()}collapse(){if(!this.expanded)return;this.clearFlatNodes(),this.expandedInternal=!1;const e=this.existingElement();e&&e.classList.remove("expanded"),this.selected&&this.dataGrid.announceSelectedGridNode(),this.dataGrid.scheduleUpdateStructure()}expand(){this.expanded||(this.dataGrid.stickToBottom=!1,this.clearFlatNodes(),super.expand(),this.dataGrid.scheduleUpdateStructure())}attached(){const e=this.existingElement();return Boolean(this.dataGrid&&e&&e.parentElement)}refresh(){this.attached()?(this.stale=!0,this.dataGrid.scheduleUpdate()):this.resetElement()}reveal(){this.dataGrid.revealViewportNode(this)}recalculateSiblings(e){this.clearFlatNodes(),super.recalculateSiblings(e)}}var A=Object.freeze({__proto__:null,ViewportDataGrid:S,ViewportDataGridNode:k});class G extends S{sortingFunction;constructor(e){super(e),this.sortingFunction=G.TrivialComparator,this.setRootNode(new T)}static TrivialComparator(e,t){return 0}static NumericComparator(e,t,i){const s=t.data[e],n=i.data[e],r=Number(s instanceof Node?s.textContent:s),l=Number(n instanceof Node?n.textContent:n);return r<l?-1:r>l?1:0}static StringComparator(e,t,i){const s=t.data[e],n=i.data[e],r=s instanceof Node?s.textContent:String(s),l=n instanceof Node?n.textContent:String(n);return r&&l?r<l?-1:r>l?1:0:0}static Comparator(e,t,i,s){return t?e(s,i):e(i,s)}static create(e,t,i){const s=e.length;if(!s)return null;const n=[];for(let t=0;t<e.length;++t){const i=String(t);n.push({id:i,title:e[t],sortable:!0})}const r=[];for(let i=0;i<t.length/s;++i){const n={};for(let r=0;r<e.length;++r)n[r]=t[s*i+r];const l=new T(n);l.selectable=!1,r.push(l)}const l=new G({displayName:i,columns:n}),d=r.length,o=l.rootNode();for(let e=0;e<d;++e)o.appendChild(r[e]);return l.addEventListener("SortingChanged",(function(){const e=l.rootNode().children,t=l.sortColumnId();if(!t)return;let i=!0;for(let s=0;s<e.length;s++){const n=e[s].data[t];if(isNaN(n instanceof Node?n.textContent:n)){i=!1;break}}const s=i?G.NumericComparator:G.StringComparator;l.sortNodes(s.bind(null,t),!l.isSortOrderAscending())})),l}insertChild(e){this.rootNode().insertChildOrdered(e)}sortNodes(e,t){this.sortingFunction=G.Comparator.bind(null,e,t),this.rootNode().recalculateSiblings(0),this.rootNode().sortChildren(),this.scheduleUpdateStructure()}}class T extends k{constructor(e,t){super(e,t)}insertChildOrdered(e){const t=this.dataGrid;t&&this.insertChild(e,i.ArrayUtilities.upperBound(this.children,e,t.sortingFunction))}sortChildren(){const e=this.dataGrid;if(e){this.children.sort(e.sortingFunction);for(let e=0;e<this.children.length;++e){this.children[e].recalculateSiblings(e)}for(let e=0;e<this.children.length;++e){this.children[e].sortChildren()}}}}var R=Object.freeze({__proto__:null,SortableDataGrid:G,SortableDataGridNode:T});const E={showDBefore:"Show {PH1} before",showDAfter:"Show {PH1} after",showAllD:"Show all {PH1}"},L=t.i18n.registerUIStrings("ui/legacy/components/data_grid/ShowMoreDataGridNode.ts",E),H=t.i18n.getLocalizedString.bind(void 0,L);var W=Object.freeze({__proto__:null,ShowMoreDataGridNode:class extends N{callback;startPosition;endPosition;chunkSize;showNext;showAll;showLast;selectable;hasCells;constructor(e,t,i,s){super({summaryRow:!0},!1),this.callback=e,this.startPosition=t,this.endPosition=i,this.chunkSize=s,this.showNext=document.createElement("button"),this.showNext.classList.add("text-button"),this.showNext.type="button",this.showNext.addEventListener("click",this.showNextChunk.bind(this),!1),this.showNext.textContent=H(E.showDBefore,{PH1:this.chunkSize}),this.showAll=document.createElement("button"),this.showAll.classList.add("text-button"),this.showAll.type="button",this.showAll.addEventListener("click",this.showAllInternal.bind(this),!1),this.showLast=document.createElement("button"),this.showLast.classList.add("text-button"),this.showLast.type="button",this.showLast.addEventListener("click",this.showLastChunk.bind(this),!1),this.showLast.textContent=H(E.showDAfter,{PH1:this.chunkSize}),this.updateLabels(),this.selectable=!1}showNextChunk(){this.callback(this.startPosition,this.startPosition+this.chunkSize)}showAllInternal(){this.callback(this.startPosition,this.endPosition)}showLastChunk(){this.callback(this.endPosition-this.chunkSize,this.endPosition)}updateLabels(){const e=this.endPosition-this.startPosition;e>this.chunkSize?(this.showNext.classList.remove("hidden"),this.showLast.classList.remove("hidden")):(this.showNext.classList.add("hidden"),this.showLast.classList.add("hidden")),this.showAll.textContent=H(E.showAllD,{PH1:e})}createCells(e){this.hasCells=!1,super.createCells(e)}createCell(e){const t=this.createTD(e);return t.classList.add("show-more"),this.hasCells||(this.hasCells=!0,this.depth&&this.dataGrid&&t.style.setProperty("padding-left",this.depth*this.dataGrid.indentWidth+"px"),t.appendChild(this.showNext),t.appendChild(this.showAll),t.appendChild(this.showLast)),t}setStartPosition(e){this.startPosition=e,this.updateLabels()}setEndPosition(e){this.endPosition=e,this.updateLabels()}nodeSelfHeight(){return 40}dispose(){}}});export{I as DataGrid,W as ShowMoreDataGridNode,R as SortableDataGrid,A as ViewportDataGrid};
